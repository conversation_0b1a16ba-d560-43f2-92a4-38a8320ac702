const Meal = require('../models/Meal');
const User = require('../models/User');
const path = require('path');
const fs = require('fs');

// Get Filipino meals with search support
exports.getFilipinoDishes = async (req, res) => {
  try {
    const filePath = path.join(__dirname, '../scripts/filipinoMealData.json');
    const mealData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // Add IDs to meals if they don't have them
    const mealsWithIds = mealData.map((meal, index) => ({
      id: meal.id || index + 1,
      ...meal
    }));

    // Search filter
    const { search } = req.query;
    let filteredMeals = mealsWithIds;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredMeals = mealsWithIds.filter(meal =>
        meal.name && meal.name.toLowerCase().includes(searchLower)
      );
    }
console.log('First meal:', mealsWithIds[0]);
    res.json(filteredMeals);
  } catch (error) {
    console.error('Error reading Filipino meal data:', error);
    res.status(500).json({ message: 'Error fetching Filipino meal data' });
  }
};

// Get a single Filipino meal by ID
exports.getFilipinoDishById = async (req, res) => {
  try {
    const filePath = path.join(__dirname, '../scripts/filipinoMealData.json');
    const mealData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    // Add IDs to meals if they don't have them
    const mealsWithIds = mealData.map((meal, index) => ({
      id: meal.id || index + 1,
      ...meal
    }));
    
    const meal = mealsWithIds.find(meal => meal.id === parseInt(req.params.id));
    
    if (!meal) {
      return res.status(404).json({ message: 'Filipino meal not found' });
    }
    
    res.json(meal);
  } catch (error) {
    console.error('Error reading Filipino meal data:', error);
    res.status(500).json({ message: 'Error fetching Filipino meal data' });
  }
};

// Get all meals
exports.getMeals = async (req, res) => {
  try {
    const { sortBy = 'name', sortOrder = 'asc' } = req.query;

    // Build sort object
    const sortOptions = {};

    // Validate sort field
    const validSortFields = ['name', 'price', 'rating', 'calories', 'prepTime', 'createdAt'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'name';

    // Validate sort order
    const order = sortOrder === 'desc' ? -1 : 1;
    sortOptions[sortField] = order;

    console.log(`Getting meals sorted by ${sortField} (${sortOrder})`);

    const meals = await Meal.find().sort(sortOptions);
    res.status(200).json(meals);
  } catch (error) {
    console.error('Error in getMeals:', error);
    res.status(500).json({ message: 'Error fetching meals', error: error.message });
  }
};

// Get meal by ID
exports.getMealById = async (req, res) => {
  try {
    const meal = await Meal.findById(req.params.id);
    if (!meal) {
      return res.status(404).json({ message: 'Meal not found' });
    }
    res.status(200).json(meal);
  } catch (error) {
    console.error('Error in getMealById:', error);
    res.status(500).json({ message: 'Error fetching meal', error: error.message });
  }
};

// Create a new meal
exports.createMeal = async (req, res) => {
  try {
    const newMeal = new Meal(req.body);
    const savedMeal = await newMeal.save();
    res.status(201).json(savedMeal);
  } catch (error) {
    console.error('Error in createMeal:', error);
    res.status(400).json({ message: 'Error creating meal', error: error.message });
  }
};

// Update a meal
exports.updateMeal = async (req, res) => {
  try {
    const updatedMeal = await Meal.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!updatedMeal) {
      return res.status(404).json({ message: 'Meal not found' });
    }
    res.status(200).json(updatedMeal);
  } catch (error) {
    console.error('Error in updateMeal:', error);
    res.status(400).json({ message: 'Error updating meal', error: error.message });
  }
};

// Delete a meal
exports.deleteMeal = async (req, res) => {
  try {
    const deletedMeal = await Meal.findByIdAndDelete(req.params.id);
    if (!deletedMeal) {
      return res.status(404).json({ message: 'Meal not found' });
    }
    res.status(200).json({ message: 'Meal deleted successfully' });
  } catch (error) {
    console.error('Error in deleteMeal:', error);
    res.status(500).json({ message: 'Error deleting meal', error: error.message });
  }
};

exports.searchMeals = async (req, res) => {
  try {
    const {
      query,
      category,
      tags,
      dietaryRestrictions,
      minCalories,
      maxCalories,
      minProtein,
      maxProtein,
      minCarbs,
      maxCarbs,
      minFat,
      maxFat,
      minPrice,
      maxPrice,
      sortBy = 'name',
      sortOrder = 'asc',
      limit = 20,
      page = 1
    } = req.query;
    
    console.log('Searching meals with filters:', req.query);
    
    // Build search query
    const searchQuery = {};
    
    // Text search if query provided
    if (query) {
      searchQuery.$text = { $search: query };
    }
    
    // Filter by category
    if (category) {
      searchQuery.category = category.toLowerCase();
    }
    
    // Filter by tags
    if (tags) {
      const tagList = tags.split(',').map(tag => tag.trim());
      searchQuery.tags = { $in: tagList };
    }
    
    // Filter by dietary restrictions
    if (dietaryRestrictions) {
      const restrictions = dietaryRestrictions.split(',').map(r => r.trim());
      
      // Map restrictions to the corresponding fields in the meal model
      const dietaryQuery = {};
      
      restrictions.forEach(restriction => {
        switch(restriction) {
          case 'vegetarian':
            dietaryQuery.isVegetarian = true;
            break;
          case 'vegan':
            dietaryQuery.isVegan = true;
            break;
          case 'gluten-free':
            dietaryQuery.isGlutenFree = true;
            break;
          case 'dairy-free':
            dietaryQuery.isDairyFree = true;
            break;
          case 'nut-free':
            dietaryQuery.isNutFree = true;
            break;
          case 'low-carb':
            dietaryQuery.isLowCarb = true;
            break;
        }
      });
      
      // Add dietary attributes to search query
      Object.keys(dietaryQuery).forEach(key => {
        searchQuery[`dietaryAttributes.${key}`] = dietaryQuery[key];
      });
    }
    
    // Filter by nutrition values
    if (minCalories) searchQuery.calories = { $gte: parseInt(minCalories) };
    if (maxCalories) {
      if (searchQuery.calories) {
        searchQuery.calories.$lte = parseInt(maxCalories);
      } else {
        searchQuery.calories = { $lte: parseInt(maxCalories) };
      }
    }
    
    if (minProtein) searchQuery.protein = { $gte: parseInt(minProtein) };
    if (maxProtein) {
      if (searchQuery.protein) {
        searchQuery.protein.$lte = parseInt(maxProtein);
      } else {
        searchQuery.protein = { $lte: parseInt(maxProtein) };
      }
    }
    
    if (minCarbs) searchQuery.carbs = { $gte: parseInt(minCarbs) };
    if (maxCarbs) {
      if (searchQuery.carbs) {
        searchQuery.carbs.$lte = parseInt(maxCarbs);
      } else {
        searchQuery.carbs = { $lte: parseInt(maxCarbs) };
      }
    }
    
    if (minFat) searchQuery.fat = { $gte: parseInt(minFat) };
    if (maxFat) {
      if (searchQuery.fat) {
        searchQuery.fat.$lte = parseInt(maxFat);
      } else {
        searchQuery.fat = { $lte: parseInt(maxFat) };
      }
    }

    // Filter by price
    if (minPrice) searchQuery.price = { $gte: parseInt(minPrice) };
    if (maxPrice) {
      if (searchQuery.price) {
        searchQuery.price.$lte = parseInt(maxPrice);
      } else {
        searchQuery.price = { $lte: parseInt(maxPrice) };
      }
    }

    console.log('Final search query:', searchQuery);

    // Build sort object
    const sortOptions = {};
    const validSortFields = ['name', 'price', 'rating', 'calories', 'prepTime', 'createdAt'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'name';
    const order = sortOrder === 'desc' ? -1 : 1;
    sortOptions[sortField] = order;

    console.log(`Sorting by ${sortField} (${sortOrder})`);

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query with pagination and sorting
    const meals = await Meal.find(searchQuery)
      .skip(skip)
      .limit(parseInt(limit))
      .sort(sortOptions);
    
    // Get total count for pagination
    const totalCount = await Meal.countDocuments(searchQuery);
    
    console.log(`Found ${meals.length} meals out of ${totalCount} total`);
    
    res.status(200).json({
      success: true,
      meals,
      pagination: {
        total: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalCount / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error searching meals:', error);
    res.status(500).json({ 
      success: false,
      message: 'Error searching meals', 
      error: error.message 
    });
  }
};

// Get meals by dietary preferences
exports.getMealsByDietaryPreferences = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`Getting meals by dietary preferences for user ${userId}`);
    
    // Get user with preferences
    const user = await User.findById(userId);
    
    if (!user) {
      console.log('User not found');
      return res.status(404).json({ 
        success: false,
        message: 'User not found' 
      });
    }
    
    // Get dietary preferences
    const preferences = user.dietaryPreferences || {};
    console.log('User preferences:', preferences);
    
    // Build query based on preferences
    const query = {};
    
    // Handle dietary restrictions
    if (preferences.restrictions && preferences.restrictions.length > 0) {
      const dietaryQuery = {};
      
      preferences.restrictions.forEach(restriction => {
        switch(restriction) {
          case 'Vegetarian':
            dietaryQuery.isVegetarian = true;
            break;
          case 'Vegan':
            dietaryQuery.isVegan = true;
            break;
          case 'Gluten-Free':
            dietaryQuery.isGlutenFree = true;
            break;
          case 'Dairy-Free':
            dietaryQuery.isDairyFree = true;
            break;
          case 'Nut-Free':
            dietaryQuery.isNutFree = true;
            break;
          case 'Low-Carb':
            dietaryQuery.isLowCarb = true;
            break;
        }
      });
      
      // Add dietary attributes to search query
      Object.keys(dietaryQuery).forEach(key => {
        query[`dietaryAttributes.${key}`] = dietaryQuery[key];
      });
    }
    
    // Handle allergies and disliked ingredients
    const excludedIngredients = [
      ...(preferences.allergies || []),
      ...(preferences.dislikedIngredients || [])
    ];
    
    if (excludedIngredients.length > 0) {
      query['ingredients.name'] = { $nin: excludedIngredients };
    }
    
    // Handle calorie and macronutrient targets
    if (preferences.calorieTarget) {
      // Get meals within 20% of calorie target
      const minCalories = preferences.calorieTarget * 0.8;
      const maxCalories = preferences.calorieTarget * 1.2;
      query.calories = { $gte: minCalories, $lte: maxCalories };
    }
    
    console.log('Query for meals by dietary preferences:', query);
    
    // Get meals based on query
    const meals = await Meal.find(query).limit(20);
    
    console.log(`Found ${meals.length} meals matching dietary preferences`);
    
    res.status(200).json({
      success: true,
      meals
    });
  } catch (error) {
    console.error('Error getting meals by dietary preferences:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get meals by dietary preferences',
      error: error.message
    });
  }
};

// Get meal suggestions for specific meal type
exports.getMealSuggestions = async (req, res) => {
  try {
    const { mealType } = req.query;
    const userId = req.user ? req.user.id : null;

    console.log(`Getting ${mealType} suggestions for user ${userId || 'anonymous'}`);

    if (!mealType) {
      console.log('Meal type is required');
      return res.status(400).json({
        success: false,
        message: 'Meal type is required'
      });
    }

    // Build query based on meal type
    const query = { category: mealType.toLowerCase() };

    // If user is logged in, apply dietary preferences
    if (userId) {
      const user = await User.findById(userId);

      if (user && user.dietaryPreferences) {
        const preferences = user.dietaryPreferences;

        // Handle dietary restrictions
        if (preferences.restrictions && preferences.restrictions.length > 0) {
          const dietaryQuery = {};

          preferences.restrictions.forEach(restriction => {
            switch(restriction) {
              case 'Vegetarian':
                dietaryQuery.isVegetarian = true;
                break;
              case 'Vegan':
                dietaryQuery.isVegan = true;
                break;
              case 'Gluten-Free':
                dietaryQuery.isGlutenFree = true;
                break;
              case 'Dairy-Free':
                dietaryQuery.isDairyFree = true;
                break;
              case 'Nut-Free':
                dietaryQuery.isNutFree = true;
                break;
              case 'Low-Carb':
                dietaryQuery.isLowCarb = true;
                break;
              case 'Keto':
                dietaryQuery.isKeto = true;
                break;
              case 'Pescatarian':
                dietaryQuery.isPescatarian = true;
                break;
              case 'Halal':
                dietaryQuery.isHalal = true;
                break;
            }
          });

          // Add dietary restrictions to search query using the new dietType field
          Object.keys(dietaryQuery).forEach(key => {
            query[`dietType.${key}`] = dietaryQuery[key];
          });
        }

        // Handle allergies and disliked ingredients
        const excludedIngredients = [
          ...(preferences.allergies || []),
          ...(preferences.dislikedIngredients || [])
        ];

        if (excludedIngredients.length > 0) {
          query['ingredients'] = { $nin: excludedIngredients };
        }
      }
    }

    console.log('Query for meal suggestions:', query);

    // Get random meals based on query
    const count = await Meal.countDocuments(query);
    const random = Math.floor(Math.random() * Math.max(count - 10, 1));

    const meals = await Meal.find(query)
      .skip(random)
      .limit(10);

    console.log(`Found ${meals.length} meal suggestions`);

    res.status(200).json({
      success: true,
      suggestions: meals
    });
  } catch (error) {
    console.error('Error getting meal suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get meal suggestions',
      error: error.message
    });
  }
};

// Get personalized meal recommendations
exports.getMealRecommendations = async (req, res) => {
  try {
    const { mealType, calorieTarget, includeFamily } = req.query;
    const userId = req.user.id;

    console.log(`Getting personalized recommendations for user ${userId}`);

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Build base query
    let query = {};

    // Filter by meal type if specified
    if (mealType) {
      query.category = { $in: [mealType.toLowerCase()] };
    }

    // Apply user dietary preferences
    const preferences = user.dietaryPreferences || {};
    const allRestrictions = [...(preferences.restrictions || [])];
    const allAllergies = [...(preferences.allergies || [])];
    const allDislikedIngredients = [...(preferences.dislikedIngredients || [])];

    // Include family member preferences if requested
    if (includeFamily === 'true' && user.familyMembers && user.familyMembers.length > 0) {
      user.familyMembers.forEach(member => {
        if (member.dietaryPreferences) {
          allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
          allAllergies.push(...(member.dietaryPreferences.allergies || []));
          allDislikedIngredients.push(...(member.dietaryPreferences.dislikedIngredients || []));
        }
      });
    }

    // Remove duplicates
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];
    const uniqueDislikedIngredients = [...new Set(allDislikedIngredients)];

    // Apply dietary restrictions
    if (uniqueRestrictions.length > 0) {
      const dietaryQuery = {};

      uniqueRestrictions.forEach(restriction => {
        switch(restriction) {
          case 'Vegetarian':
            dietaryQuery['dietType.isVegetarian'] = true;
            break;
          case 'Vegan':
            dietaryQuery['dietType.isVegan'] = true;
            break;
          case 'Gluten-Free':
            dietaryQuery['dietType.isGlutenFree'] = true;
            break;
          case 'Dairy-Free':
            dietaryQuery['dietType.isDairyFree'] = true;
            break;
          case 'Nut-Free':
            dietaryQuery['dietType.isNutFree'] = true;
            break;
          case 'Low-Carb':
            dietaryQuery['dietType.isLowCarb'] = true;
            break;
          case 'Keto':
            dietaryQuery['dietType.isKeto'] = true;
            break;
          case 'Pescatarian':
            dietaryQuery['dietType.isPescatarian'] = true;
            break;
          case 'Halal':
            dietaryQuery['dietType.isHalal'] = true;
            break;
        }
      });

      Object.assign(query, dietaryQuery);
    }

    // Exclude allergies and disliked ingredients
    const excludedIngredients = [...uniqueAllergies, ...uniqueDislikedIngredients];
    if (excludedIngredients.length > 0) {
      // Use $and with $not and $regex for each allergen to check ingredients array
      const allergenQueries = excludedIngredients.map(allergen => ({
        ingredients: {
          $not: {
            $elemMatch: {
              $regex: new RegExp(allergen, 'i')
            }
          }
        }
      }));

      // Also check the allergens field if it exists
      const allergenFieldQueries = excludedIngredients.map(allergen => ({
        allergens: {
          $not: {
            $elemMatch: {
              $regex: new RegExp(allergen, 'i')
            }
          }
        }
      }));

      query.$and = [...(query.$and || []), ...allergenQueries, ...allergenFieldQueries];
    }

    // Apply calorie filtering if specified
    if (calorieTarget) {
      const target = parseInt(calorieTarget);
      const mealCalorieTarget = target / (preferences.mealFrequency || 3);
      const tolerance = mealCalorieTarget * 0.3; // 30% tolerance

      query.calories = {
        $gte: Math.max(mealCalorieTarget - tolerance, 0),
        $lte: mealCalorieTarget + tolerance
      };
    }

    console.log('Recommendation query:', JSON.stringify(query, null, 2));

    // Get meals with scoring
    const meals = await Meal.find(query).limit(20);

    // Score meals based on user preferences and history
    const scoredMeals = await scoreMealsForUser(meals, user);

    // Sort by score and return top recommendations
    const recommendations = scoredMeals
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
      .map(item => ({
        ...item.meal.toObject(),
        recommendationScore: item.score,
        recommendationReasons: item.reasons
      }));

    console.log(`Found ${recommendations.length} personalized recommendations`);

    res.status(200).json({
      success: true,
      recommendations,
      appliedFilters: {
        restrictions: uniqueRestrictions,
        allergies: uniqueAllergies,
        dislikedIngredients: uniqueDislikedIngredients,
        calorieTarget: calorieTarget ? parseInt(calorieTarget) : null,
        includeFamily: includeFamily === 'true'
      }
    });
  } catch (error) {
    console.error('Error getting meal recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get meal recommendations',
      error: error.message
    });
  }
};

// Helper function to score meals for a user
const scoreMealsForUser = async (meals, user) => {
  const scoredMeals = [];

  for (const meal of meals) {
    let score = 0;
    const reasons = [];

    // Base score
    score += 50;

    // Rating boost
    if (meal.rating) {
      score += meal.rating * 10;
      if (meal.rating >= 4) {
        reasons.push('Highly rated meal');
      }
    }

    // Check if user has favorited this meal
    const isFavorite = user.favoriteMeals && user.favoriteMeals.some(fav =>
      fav.name === meal.name
    );
    if (isFavorite) {
      score += 30;
      reasons.push('One of your favorites');
    }

    // Check if user has viewed this meal recently
    const recentlyViewed = user.recentlyViewedMeals && user.recentlyViewedMeals.some(viewed =>
      viewed.name === meal.name
    );
    if (recentlyViewed) {
      score += 15;
      reasons.push('Recently viewed');
    }

    // Nutritional alignment with preferences
    if (user.dietaryPreferences && user.dietaryPreferences.calorieTarget) {
      const targetCaloriesPerMeal = user.dietaryPreferences.calorieTarget / (user.dietaryPreferences.mealFrequency || 3);
      const caloriesDiff = Math.abs(meal.calories - targetCaloriesPerMeal);
      const calorieScore = Math.max(0, 20 - (caloriesDiff / targetCaloriesPerMeal) * 20);
      score += calorieScore;

      if (calorieScore > 15) {
        reasons.push('Matches your calorie goals');
      }
    }

    // Dietary compliance bonus
    if (user.dietaryPreferences && user.dietaryPreferences.restrictions) {
      let compliantCount = 0;
      user.dietaryPreferences.restrictions.forEach(restriction => {
        switch(restriction) {
          case 'Vegetarian':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isVegetarian) {
              compliantCount++;
            }
            break;
          case 'Vegan':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isVegan) {
              compliantCount++;
            }
            break;
          case 'Gluten-Free':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isGlutenFree) {
              compliantCount++;
            }
            break;
          case 'Dairy-Free':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isDairyFree) {
              compliantCount++;
            }
            break;
          case 'Nut-Free':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isNutFree) {
              compliantCount++;
            }
            break;
          case 'Low-Carb':
            if (meal.dietaryAttributes && meal.dietaryAttributes.isLowCarb) {
              compliantCount++;
            }
            break;
        }
      });

      if (compliantCount > 0) {
        score += compliantCount * 15;
        reasons.push('Matches your dietary restrictions');
      }
    }

    // Variety bonus (penalize if user has added this meal recently)
    const recentlyAdded = user.recentlyAddedToMealPlans && user.recentlyAddedToMealPlans.some(added => {
      const addedDate = new Date(added.addedAt);
      const daysSince = (Date.now() - addedDate.getTime()) / (1000 * 60 * 60 * 24);
      return added.name === meal.name && daysSince < 7;
    });

    if (recentlyAdded) {
      score -= 20;
    } else {
      score += 10;
      reasons.push('Adds variety to your meal plan');
    }

    scoredMeals.push({
      meal,
      score: Math.max(0, score),
      reasons
    });
  }

  return scoredMeals;
};

// Get popular meals
exports.getPopularMeals = async (req, res) => {
  try {
    console.log('Getting popular meals');
    
    // This would typically be based on some popularity metric
    // For now, we'll just return some random meals
    const meals = await Meal.aggregate([
      { $sample: { size: 10 } }
    ]);
    
    console.log(`Found ${meals.length} popular meals`);
    
    res.status(200).json({
      success: true,
      popularMeals: meals
    });
  } catch (error) {
    console.error('Error getting popular meals:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get popular meals',
      error: error.message
    });
  }
};
