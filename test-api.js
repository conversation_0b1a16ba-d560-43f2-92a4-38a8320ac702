const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

// Test the new meal recommendations endpoint
async function testMealRecommendations() {
  try {
    console.log('Testing meal recommendations endpoint...');
    
    // First, let's test without authentication (should work for basic recommendations)
    const response = await axios.get(`${BASE_URL}/meals/recommendations`, {
      params: {
        mealType: 'breakfast',
        includeFamily: 'false'
      }
    });
    
    console.log('✅ Meal recommendations response:', response.data);
    
  } catch (error) {
    console.error('❌ Error testing meal recommendations:', error.response?.data || error.message);
  }
}

// Test the generate meal plan endpoint
async function testGenerateMealPlan() {
  try {
    console.log('Testing generate meal plan endpoint...');
    
    const response = await axios.post(`${BASE_URL}/meal-plans/generate`, {
      startDate: '2024-01-15',
      endDate: '2024-01-17',
      includeFamily: false,
      calorieTarget: 2000
    });
    
    console.log('✅ Generate meal plan response:', response.data);
    
  } catch (error) {
    console.error('❌ Error testing generate meal plan:', error.response?.data || error.message);
  }
}

// Test basic meals endpoint
async function testBasicMeals() {
  try {
    console.log('Testing basic meals endpoint...');
    
    const response = await axios.get(`${BASE_URL}/meals/filipino`);
    
    console.log('✅ Basic meals response:', {
      success: response.data.success,
      count: response.data.meals?.length || 0,
      firstMeal: response.data.meals?.[0]?.name || 'No meals'
    });
    
  } catch (error) {
    console.error('❌ Error testing basic meals:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🧪 Starting API tests...\n');
  
  await testBasicMeals();
  console.log('\n---\n');
  
  await testMealRecommendations();
  console.log('\n---\n');
  
  await testGenerateMealPlan();
  
  console.log('\n🏁 Tests completed!');
}

runTests();
