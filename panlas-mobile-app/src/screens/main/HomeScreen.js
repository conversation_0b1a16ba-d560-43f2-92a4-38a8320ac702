import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  ScrollView,
  RefreshControl,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mealsAPI, userAPI, mealPlansAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const HomeScreen = ({ navigation }) => {
  const [meals, setMeals] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [popularMeals, setPopularMeals] = useState([]);
  const [recommendedMeals, setRecommendedMeals] = useState([]);
  const [recentlyViewed, setRecentlyViewed] = useState([]);
  const [userPreferences, setUserPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('name'); // 'name', 'price-low', 'price-high'
  const [showSortModal, setShowSortModal] = useState(false);

  // Quick meal picker modal state
  const [showMealPicker, setShowMealPicker] = useState(false);
  const [selectedMealForPicker, setSelectedMealForPicker] = useState(null);
  const [selectedDates, setSelectedDates] = useState([]);
  const [selectedMealTypes, setSelectedMealTypes] = useState([]);
  const [isCreatingMealPlan, setIsCreatingMealPlan] = useState(false);

  const { user } = useAuth();
  const { isFavorite, addFavorite, removeFavorite } = useFavorites();

  const categories = ['All', 'Breakfast', 'Lunch', 'Dinner', 'Soup', 'Grilled', 'Meat', 'Vegetable', 'Rice Cake'];

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    filterMeals();
  }, [meals, searchQuery, selectedCategory, sortBy, userPreferences]);

  const loadUserPreferences = async () => {
    try {
      if (user) {
        const response = await userAPI.getDietaryPreferences();
        console.log('Loaded user preferences:', response);

        if (response.data && response.data.success && response.data.dietaryPreferences) {
          console.log('Setting user preferences to (from data):', response.data.dietaryPreferences);
          setUserPreferences(response.data.dietaryPreferences);
        } else if (response.success && response.dietaryPreferences) {
          console.log('Setting user preferences to (direct):', response.dietaryPreferences);
          setUserPreferences(response.dietaryPreferences);
        } else {
          console.log('No dietary preferences found, setting empty object');
          setUserPreferences({});
        }
      } else {
        console.log('No user logged in, setting empty preferences');
        setUserPreferences({});
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setUserPreferences({});
    }
  };

  const loadInitialData = async () => {
    try {
      setLoading(true);
      // Load user preferences first, then meals so filtering works correctly
      await loadUserPreferences();
      await loadMeals();
      await loadPopularMeals();
      await loadRecommendedMeals();
      await loadRecentlyViewed();
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMeals = async () => {
    try {
      const response = await mealsAPI.getFilipinoDishes();
      const mealsData = response?.data || [];

      // Ensure mealsData is an array
      if (Array.isArray(mealsData)) {
        setMeals(mealsData);
        setFilteredMeals(mealsData);
      } else {
        console.warn('Meals response is not an array:', mealsData);
        setMeals([]);
        setFilteredMeals([]);
      }
    } catch (error) {
      console.error('Error loading meals:', error);
      setMeals([]);
      setFilteredMeals([]);
      Alert.alert('Error', 'Failed to load meals. Please check your internet connection.');
    }
  };

  const loadPopularMeals = async () => {
    try {
      const response = await mealsAPI.getPopularMeals();
      const meals = response?.data || [];

      // Ensure meals is an array
      if (Array.isArray(meals)) {
        setPopularMeals(meals);
      } else {
        console.warn('Popular meals response is not an array:', meals);
        setPopularMeals([]);
      }
    } catch (error) {
      console.error('Error loading popular meals:', error);
      setPopularMeals([]);
    }
  };

  const loadRecommendedMeals = async () => {
    try {
      if (user) {
        // Get recommendations based on user's dietary preferences only
        const response = await mealsAPI.getMealRecommendations({
          includeFamily: 'false'
        });
        const recommendedMeals = response?.data || [];

        // Ensure recommendedMeals is an array and limit to 5 items
        if (Array.isArray(recommendedMeals)) {
          setRecommendedMeals(recommendedMeals.slice(0, 5));
        } else {
          console.warn('Recommended meals response is not an array:', recommendedMeals);
          setRecommendedMeals([]);
        }
      } else {
        // For non-logged in users, use popular meals as recommendations
        const response = await mealsAPI.getPopularMeals();
        const allMeals = response?.data || [];

        if (Array.isArray(allMeals)) {
          setRecommendedMeals(allMeals.slice(0, 5));
        } else {
          console.warn('Popular meals response is not an array:', allMeals);
          setRecommendedMeals([]);
        }
      }
    } catch (error) {
      console.error('Error loading recommended meals:', error);
      // Fallback to popular meals if recommendations fail
      try {
        const response = await mealsAPI.getPopularMeals();
        const allMeals = response?.data || [];
        if (Array.isArray(allMeals)) {
          setRecommendedMeals(allMeals.slice(0, 5));
        } else {
          setRecommendedMeals([]);
        }
      } catch (fallbackError) {
        console.error('Error loading fallback meals:', fallbackError);
        setRecommendedMeals([]);
      }
    }
  };

  const loadRecentlyViewed = async () => {
    try {
      const response = await userAPI.getRecentlyViewedMeals();
      setRecentlyViewed(response.data || []);
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
    }
  };

  const getPriceValue = (meal) => {
    // Use actual price field from the meal data
    return meal.price || 0;
  };

  const filterMeals = () => {
    let filtered = meals;

    console.log('🔍 Filtering meals with preferences:', userPreferences);
    console.log('📊 Total meals before filtering:', meals.length);

    // Filter by user dietary preferences first
    if (userPreferences && Object.keys(userPreferences).length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};

        // Check dietary restrictions
        if (userPreferences.restrictions && userPreferences.restrictions.length > 0) {
          const meetsRestrictions = userPreferences.restrictions.every(restriction => {
            let meets = false;
            switch(restriction) {
              case 'Vegetarian':
                meets = dietType.isVegetarian;
                break;
              case 'Vegan':
                meets = dietType.isVegan;
                break;
              case 'Gluten-Free':
                meets = dietType.isGlutenFree;
                break;
              case 'Dairy-Free':
                meets = dietType.isDairyFree;
                break;
              case 'Nut-Free':
                meets = dietType.isNutFree;
                break;
              case 'Low-Carb':
                meets = dietType.isLowCarb;
                break;
              case 'Keto':
                meets = dietType.isKeto;
                break;
              case 'Pescatarian':
                meets = dietType.isPescatarian;
                break;
              case 'Halal':
                meets = dietType.isHalal;
                break;
              default:
                meets = true;
            }

            return meets;
          });
          if (!meetsRestrictions) return false;
        }

        // Check allergies - exclude meals with allergens
        if (userPreferences.allergies && userPreferences.allergies.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasAllergen = userPreferences.allergies.some(allergy =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(allergy.toLowerCase())
            )
          );
          if (hasAllergen) {
            console.log(`❌ Meal "${meal.name}" contains allergen "${userPreferences.allergies.join(', ')}" in ingredients: ${ingredients.join(', ')}`);
            return false;
          }
        }

        // Note: Calorie filtering removed to maintain consistency with backend and CreateMealPlan
        // Users can filter by calories manually if needed

        return true;
      });
    }

    // Filter by category/meal type
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(meal => {
        // Check if it's a meal type filter (Breakfast, Lunch, Dinner)
        if (['Breakfast', 'Lunch', 'Dinner'].includes(selectedCategory)) {
          // Use mealType field for meal time filtering
          if (Array.isArray(meal.mealType)) {
            return meal.mealType.some(type =>
              type.toLowerCase() === selectedCategory.toLowerCase()
            );
          } else if (meal.mealType) {
            return meal.mealType.toLowerCase() === selectedCategory.toLowerCase();
          }
          return false;
        } else {
          // Use category field for other filters (Soup, Grilled, etc.)
          if (Array.isArray(meal.category)) {
            return meal.category.some(cat =>
              cat.toLowerCase() === selectedCategory.toLowerCase()
            );
          } else if (meal.category) {
            return meal.category.toLowerCase() === selectedCategory.toLowerCase();
          }
          return false;
        }
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (meal.description && meal.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Sort meals
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return getPriceValue(a) - getPriceValue(b);
        case 'price-high':
          return getPriceValue(b) - getPriceValue(a);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    console.log('✅ Meals after filtering:', filtered.length);
    if (filtered.length > 0) {
      console.log('📋 Sample filtered meals:', filtered.slice(0, 3).map(m => m.name));
    }

    setFilteredMeals(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleMealPress = async (meal) => {
    try {
      // Add to recently viewed
      await userAPI.addRecentlyViewedMeal({ meal });
      // Navigate to meal detail
      navigation.navigate('MealDetail', { meal });
    } catch (error) {
      console.error('Error adding to recently viewed:', error);
      // Still navigate even if recently viewed fails
      navigation.navigate('MealDetail', { meal });
    }
  };

  const handleFavoritePress = async (meal) => {
    try {
      const mealId = meal.id || meal._id;
      if (isFavorite(mealId)) {
        await removeFavorite(mealId);
      } else {
        await addFavorite(meal);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorites');
    }
  };

  const handleAddToMealPlan = (meal) => {
    setSelectedMealForPicker(meal);
    setSelectedDates([]);
    setSelectedMealTypes([]);
    setShowMealPicker(true);
  };

  const toggleDateSelection = (date) => {
    setSelectedDates(prev =>
      prev.includes(date)
        ? prev.filter(d => d !== date)
        : [...prev, date]
    );
  };

  const toggleMealTypeSelection = (mealType) => {
    setSelectedMealTypes(prev =>
      prev.includes(mealType)
        ? prev.filter(mt => mt !== mealType)
        : [...prev, mealType]
    );
  };

  // Helper function to get valid meal ID for backend
  const getMealIdForBackend = (meal) => {
    // The backend meals array expects ObjectId references
    // Prioritize _id over id, and ensure it's a valid MongoDB ObjectId
    const mealId = meal._id;

    if (!mealId) {
      console.error('No _id found in meal object:', meal);
      throw new Error('Meal object must have a valid _id property');
    }

    // Ensure it's a valid ObjectId format (24 character hex string)
    if (typeof mealId !== 'string' || mealId.length !== 24) {
      console.error('Invalid ObjectId format:', mealId);
      throw new Error('Meal _id must be a valid 24-character ObjectId');
    }

    console.log('Meal ID being sent:', mealId, 'Type:', typeof mealId, 'Original meal:', meal.name);
    return mealId;
  };

  const handleCreateMealPlans = async () => {
    if (selectedDates.length === 0 || selectedMealTypes.length === 0) {
      Alert.alert('Selection Required', 'Please select at least one date and one meal type.');
      return;
    }

    try {
      setIsCreatingMealPlan(true);
      const meal = selectedMealForPicker;

      // Create meal plans for each combination of date and meal type
      const promises = [];
      for (const date of selectedDates) {
        for (const mealType of selectedMealTypes) {
          const mealId = getMealIdForBackend(meal);

          promises.push(
            mealPlansAPI.createOrUpdateMealPlan({
              date: date,
              mealType: mealType,
              meal: mealId
            })
          );

          // Also track for history
          promises.push(
            userAPI.addRecentlyAddedToMealPlan({
              meal,
              addedToDate: date,
              addedToMealType: mealType
            })
          );
        }
      }

      await Promise.all(promises);

      // Close modal and reset state
      setShowMealPicker(false);
      setSelectedMealForPicker(null);
      setSelectedDates([]);
      setSelectedMealTypes([]);

      // Show success message
      const dateText = selectedDates.length === 1 ? selectedDates[0] : `${selectedDates.length} dates`;
      const mealTypeText = selectedMealTypes.length === 1 ? selectedMealTypes[0] : `${selectedMealTypes.length} meal types`;

      Alert.alert(
        'Success!',
        `${meal.name} has been added to ${mealTypeText} for ${dateText}`,
        [
          {
            text: 'View Meal Plans',
            onPress: () => navigation.navigate('MealPlans')
          },
          { text: 'OK' }
        ]
      );
    } catch (error) {
      console.error('Error creating meal plans:', error);
      Alert.alert('Error', 'Failed to create meal plans. Please try again.');
    } finally {
      setIsCreatingMealPlan(false);
    }
  };

  const getPriceIndicator = (meal) => {
    // Show actual price if available, otherwise show price category
    if (meal.price && meal.price > 0) {
      return `₱${meal.price}`;
    }

    // Fallback to price category based on calories and complexity
    const calories = meal.calories || 0;
    const hasComplexIngredients = meal.ingredients && meal.ingredients.length > 8;

    if (calories < 200 && !hasComplexIngredients) {
      return '₱'; // Affordable
    } else if (calories < 400 || hasComplexIngredients) {
      return '₱₱'; // Moderate
    } else {
      return '₱₱₱'; // Premium
    }
  };

  const renderMealCard = ({ item: meal }) => (
    <TouchableOpacity
      style={commonStyles.foodCard}
      onPress={() => handleMealPress(meal)}
    >
      <View style={commonStyles.foodCardImage}>
        <Image
          source={{ uri: meal.image || 'https://via.placeholder.com/300x200' }}
          style={styles.mealImage}
          resizeMode="cover"
        />
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleFavoritePress(meal)}
        >
          <Ionicons
            name={isFavorite(meal.id || meal._id) ? "heart" : "heart-outline"}
            size={24}
            color={isFavorite(meal.id || meal._id) ? colors.secondary : colors.surface}
          />
        </TouchableOpacity>
      </View>
      <View style={commonStyles.foodCardContent}>
        <Text style={commonStyles.foodCardTitle} numberOfLines={2}>
          {meal.name}
        </Text>
        <View style={commonStyles.foodCardMeta}>
          <View style={commonStyles.categoryTag}>
            <Text style={commonStyles.categoryTagText}>
              {Array.isArray(meal.category) ? meal.category[0] : meal.category}
            </Text>
          </View>
          <View style={styles.priceIndicator}>
            <Text style={styles.priceText}>{getPriceIndicator(meal)}</Text>
          </View>
          <View style={commonStyles.rating}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={commonStyles.ratingText}>
              {meal.rating || '4.5'}
            </Text>
          </View>
        </View>
        {meal.description && (
          <Text style={styles.mealDescription} numberOfLines={2}>
            {meal.description}
          </Text>
        )}
        <View style={styles.mealActions}>
          <TouchableOpacity
            style={styles.addToPlanButton}
            onPress={() => handleAddToMealPlan(meal)}
          >
            <Ionicons name="calendar-outline" size={16} color={colors.primary} />
            <Text style={styles.addToPlanText}>Add to Meal</Text>
          </TouchableOpacity>
          <View style={styles.caloriesInfo}>
            <Text style={styles.caloriesText}>{meal.calories || 0} cal</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryFilter = ({ item: category }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        selectedCategory === category && styles.categoryButtonActive
      ]}
      onPress={() => setSelectedCategory(category)}
    >
      <Text style={[
        styles.categoryButtonText,
        selectedCategory === category && styles.categoryButtonTextActive
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  const renderPopularMeal = ({ item: meal }) => (
    <TouchableOpacity
      style={styles.popularMealCard}
      onPress={() => handleMealPress(meal)}
    >
      <Image
        source={{ uri: meal.image || 'https://via.placeholder.com/150x100' }}
        style={styles.popularMealImage}
        resizeMode="cover"
      />
      <Text style={styles.popularMealName} numberOfLines={2}>
        {meal.name}
      </Text>
    </TouchableOpacity>
  );

  // Quick Meal Picker Modal Component
  const renderQuickMealPicker = () => {
    if (!selectedMealForPicker) return null;

    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const formatDate = (date) => date.toISOString().split('T')[0];
    const formatDisplayDate = (date) => {
      const options = { weekday: 'short', month: 'short', day: 'numeric' };
      return date.toLocaleDateString('en-US', options);
    };

    const mealTypes = [
      { key: 'breakfast', label: 'Breakfast', icon: 'sunny-outline' },
      { key: 'lunch', label: 'Lunch', icon: 'partly-sunny-outline' },
      { key: 'dinner', label: 'Dinner', icon: 'moon-outline' },
      { key: 'snack', label: 'Snack', icon: 'cafe-outline' }
    ];

    const dates = [
      { key: formatDate(today), label: `Today (${formatDisplayDate(today)})` },
      { key: formatDate(tomorrow), label: `Tomorrow (${formatDisplayDate(tomorrow)})` },
      { key: formatDate(dayAfterTomorrow), label: formatDisplayDate(dayAfterTomorrow) }
    ];

    const canCreate = selectedDates.length > 0 && selectedMealTypes.length > 0;

    return (
      <Modal
        visible={showMealPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMealPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add to Meal Plan</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowMealPicker(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalScrollView}
              showsVerticalScrollIndicator={false}
              bounces={false}
            >

            {/* Meal Info */}
            <View style={styles.modalMealInfo}>
              <Image
                source={{ uri: selectedMealForPicker.image || 'https://via.placeholder.com/80x60' }}
                style={styles.modalMealImage}
                resizeMode="cover"
              />
              <View style={styles.modalMealDetails}>
                <Text style={styles.modalMealName} numberOfLines={2}>
                  {selectedMealForPicker.name}
                </Text>
                <Text style={styles.modalMealCalories}>
                  {selectedMealForPicker.calories || 0} calories
                </Text>
              </View>
            </View>

            {/* Date Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Dates ({selectedDates.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {dates.map((date) => {
                  const isSelected = selectedDates.includes(date.key);
                  return (
                    <TouchableOpacity
                      key={date.key}
                      style={[
                        styles.modalDateOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleDateSelection(date.key)}
                    >
                      <Ionicons
                        name={isSelected ? "calendar" : "calendar-outline"}
                        size={20}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {date.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Meal Type Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Meal Types ({selectedMealTypes.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {mealTypes.map((mealType) => {
                  const isSelected = selectedMealTypes.includes(mealType.key);
                  return (
                    <TouchableOpacity
                      key={mealType.key}
                      style={[
                        styles.modalMealTypeOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleMealTypeSelection(mealType.key)}
                    >
                      <Ionicons
                        name={mealType.icon}
                        size={24}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {mealType.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              {/* Create Meal Plans Button */}
              <TouchableOpacity
                style={[
                  styles.modalCreateButton,
                  !canCreate && styles.modalCreateButtonDisabled
                ]}
                onPress={handleCreateMealPlans}
                disabled={!canCreate || isCreatingMealPlan}
              >
                {isCreatingMealPlan ? (
                  <ActivityIndicator size="small" color={colors.surface} />
                ) : (
                  <>
                    <Ionicons name="add-circle" size={20} color={colors.surface} />
                    <Text style={styles.modalCreateButtonText}>
                      Create Meal Plans
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              {/* Advanced Options */}
              <TouchableOpacity
                style={styles.modalAdvancedButton}
                onPress={() => {
                  setShowMealPicker(false);
                  setSelectedMealForPicker(null);
                  setSelectedDates([]);
                  setSelectedMealTypes([]);
                  navigation.navigate('MealPlans', {
                    screen: 'CreateMealPlan',
                    params: { selectedMeal: selectedMealForPicker }
                  });
                }}
              >
                <Ionicons name="settings-outline" size={16} color={colors.primary} />
                <Text style={styles.modalAdvancedText}>Advanced Options</Text>
              </TouchableOpacity>
            </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={commonStyles.loadingText}>Loading Filipino meals...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>Hello, {user?.firstName || 'User'}!</Text>
          <Text style={styles.subGreeting}>What would you like to cook today?</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Search')}
          >
            <Ionicons name="search-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Chat')}
          >
            <Ionicons name="chatbubble-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
      >
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search Filipino dishes..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.textSecondary}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => setShowSortModal(true)}
          >
            <Ionicons name="funnel-outline" size={20} color={colors.primary} />
            <Text style={styles.sortButtonText}>Sort</Text>
          </TouchableOpacity>
        </View>

        {/* Category Filters */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <FlatList
            data={categories}
            renderItem={renderCategoryFilter}
            keyExtractor={(item) => item}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          />
        </View>

        {/* Recommended Meals */}
        {user && recommendedMeals.length > 0 && (
          <View style={styles.recommendedSection}>
            <Text style={styles.sectionTitle}>Recommended for You</Text>
            <FlatList
              data={recommendedMeals}
              renderItem={renderPopularMeal}
              keyExtractor={(item) => (item.id || item._id).toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.popularContainer}
            />
          </View>
        )}

        {/* Popular Meals */}
        {popularMeals.length > 0 && (
          <View style={styles.popularSection}>
            <Text style={styles.sectionTitle}>Popular Dishes</Text>
            <FlatList
              data={popularMeals}
              renderItem={renderPopularMeal}
              keyExtractor={(item) => (item.id || item._id).toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.popularContainer}
            />
          </View>
        )}

        {/* Recently Viewed */}
        {recentlyViewed.length > 0 && (
          <View style={styles.recentSection}>
            <Text style={styles.sectionTitle}>Recently Viewed</Text>
            <FlatList
              data={recentlyViewed.slice(0, 5)}
              renderItem={renderPopularMeal}
              keyExtractor={(item) => (item.id || item._id).toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.popularContainer}
            />
          </View>
        )}

        {/* All Meals */}
        <View style={styles.allMealsSection}>
          <Text style={styles.sectionTitle}>
            {searchQuery || selectedCategory !== 'All'
              ? `${filteredMeals.length} Results`
              : 'All Filipino Dishes'
            }
          </Text>
          {filteredMeals.length > 0 ? (
            <View style={styles.mealsGrid}>
              {filteredMeals.map((meal) => (
                <View key={meal.id || meal._id} style={styles.mealCardWrapper}>
                  {renderMealCard({ item: meal })}
                </View>
              ))}
            </View>
          ) : (
            <View style={commonStyles.emptyContainer}>
              <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
              <Text style={commonStyles.emptyTitle}>No meals found</Text>
              <Text style={commonStyles.emptySubtitle}>
                Try adjusting your search or category filter
              </Text>
              <TouchableOpacity
                style={commonStyles.primaryButton}
                onPress={() => {
                  setSearchQuery('');
                  setSelectedCategory('All');
                }}
              >
                <Text style={commonStyles.primaryButtonText}>Clear Filters</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Quick Meal Picker Modal */}
      {renderQuickMealPicker()}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSortModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.sortModalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Sort by</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowSortModal(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.sortOptionsContainer}>
              {[
                { key: 'name', label: 'Name (A-Z)', icon: 'text-outline' },
                { key: 'price-low', label: 'Price (Low to High)', icon: 'arrow-up-outline' },
                { key: 'price-high', label: 'Price (High to Low)', icon: 'arrow-down-outline' }
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.sortOption,
                    sortBy === option.key && styles.sortOptionSelected
                  ]}
                  onPress={() => {
                    setSortBy(option.key);
                    setShowSortModal(false);
                  }}
                >
                  <Ionicons
                    name={option.icon}
                    size={20}
                    color={sortBy === option.key ? colors.surface : colors.text}
                  />
                  <Text style={[
                    styles.sortOptionText,
                    sortBy === option.key && styles.sortOptionTextSelected
                  ]}>
                    {option.label}
                  </Text>
                  {sortBy === option.key && (
                    <Ionicons name="checkmark" size={20} color={colors.surface} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: spacing.sm,
    marginLeft: spacing.sm,
  },
  greeting: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  subGreeting: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  searchContainer: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.sm,
  },

  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  sortButtonText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  categoriesSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  categoriesContainer: {
    paddingHorizontal: spacing.md,
  },
  categoryButton: {
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  categoryButtonTextActive: {
    color: colors.surface,
  },
  recommendedSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  popularSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  recentSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  popularContainer: {
    paddingHorizontal: spacing.md,
  },
  popularMealCard: {
    width: 150,
    marginRight: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    overflow: 'hidden',
  },
  popularMealImage: {
    width: '100%',
    height: 100,
  },
  popularMealName: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.text,
    padding: spacing.sm,
  },
  allMealsSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    paddingBottom: spacing.xxl,
  },
  mealsGrid: {
    paddingHorizontal: spacing.md,
  },
  mealCardWrapper: {
    marginBottom: spacing.md,
  },
  mealImage: {
    width: '100%',
    height: 200,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: borderRadius.round,
    padding: spacing.sm,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 18,
  },
  mealActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  addToPlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addToPlanText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  priceIndicator: {
    backgroundColor: colors.secondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    marginHorizontal: spacing.xs,
  },
  priceText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
  },
  caloriesInfo: {
    alignItems: 'flex-end',
  },
  caloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    maxHeight: '85%',
    minHeight: '60%',
  },
  modalScrollView: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: spacing.sm,
  },
  modalMealInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalMealImage: {
    width: 60,
    height: 45,
    borderRadius: borderRadius.small,
    marginRight: spacing.md,
  },
  modalMealDetails: {
    flex: 1,
  },
  modalMealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  modalMealCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  modalSection: {
    paddingVertical: spacing.lg,
  },
  modalSectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  modalOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  modalDateOption: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 50,
  },
  modalMealTypeOption: {
    width: '48%',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 70,
    position: 'relative',
  },
  modalOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  modalOptionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  modalOptionTextSelected: {
    color: colors.surface,
  },
  modalCheckmark: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
  },
  modalActions: {
    paddingTop: spacing.lg,
    gap: spacing.md,
  },
  modalCreateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    minHeight: 50,
  },
  modalCreateButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.6,
  },
  modalCreateButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  modalAdvancedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: spacing.md,
  },
  modalAdvancedText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },

  // Sort Modal Styles
  sortModalContent: {
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    maxHeight: '50%',
  },
  sortOptionsContainer: {
    paddingVertical: spacing.md,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  sortOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  sortOptionText: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.md,
    fontWeight: '500',
  },
  sortOptionTextSelected: {
    color: colors.surface,
  },
});

export default HomeScreen;
