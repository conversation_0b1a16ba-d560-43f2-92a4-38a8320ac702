import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';
import { mealsAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';

const MealFilterScreen = ({ navigation, route }) => {
  const { user } = useAuth();
  const [selectedSort, setSelectedSort] = useState(route.params?.currentSort || 'name');
  const [userPreferences, setUserPreferences] = useState({});

  const sortOptions = [
    {
      key: 'name',
      label: 'A to Z',
      icon: 'text-outline',
      description: 'Sort alphabetically by name'
    },
    {
      key: 'price-low',
      label: 'Price: Low to High',
      icon: 'trending-up-outline',
      description: 'Sort by price (lowest first)'
    },
    {
      key: 'price-high',
      label: 'Price: High to Low',
      icon: 'trending-down-outline',
      description: 'Sort by price (highest first)'
    }
  ];

  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      if (user) {
        const response = await mealsAPI.getUserDietaryPreferences();
        const preferences = response?.data?.dietaryPreferences || {};
        setUserPreferences(preferences);
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const handleSortSelection = (sortKey) => {
    setSelectedSort(sortKey);
    // Apply the sort and navigate back to home
    if (route.params?.onSortChange) {
      route.params.onSortChange(sortKey);
    }
    navigation.goBack();
  };

  const resetSort = () => {
    setSelectedSort('name');
    // Reset sort and navigate back to home
    if (route.params?.onSortChange) {
      route.params.onSortChange('name');
    }
    navigation.goBack();
  };

  const renderSortOption = ({ item }) => {
    const isSelected = selectedSort === item.key;

    return (
      <TouchableOpacity
        style={[
          styles.sortOption,
          isSelected && styles.sortOptionSelected
        ]}
        onPress={() => handleSortSelection(item.key)}
      >
        <View style={styles.sortOptionContent}>
          <Ionicons
            name={item.icon}
            size={24}
            color={isSelected ? colors.surface : colors.primary}
          />
          <View style={styles.sortOptionText}>
            <Text style={[
              styles.sortOptionLabel,
              isSelected && styles.sortOptionLabelSelected
            ]}>
              {item.label}
            </Text>
            <Text style={[
              styles.sortOptionDescription,
              isSelected && styles.sortOptionDescriptionSelected
            ]}>
              {item.description}
            </Text>
          </View>
          {isSelected && (
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={colors.surface}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };



  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sort & Filter</Text>
        <TouchableOpacity onPress={resetSort}>
          <Text style={styles.clearButton}>Reset</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Sort Section */}
        <View style={styles.sortSection}>
          <Text style={styles.sectionTitle}>Sort Options</Text>
          <Text style={styles.sectionSubtitle}>
            Choose how to sort your meals. Dietary restrictions are applied automatically based on your preferences.
          </Text>
          <FlatList
            data={sortOptions}
            renderItem={renderSortOption}
            keyExtractor={(item) => item.key}
            scrollEnabled={false}
            contentContainerStyle={styles.sortContainer}
          />
        </View>

        {/* Current Dietary Filters Info */}
        {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
          <View style={styles.dietaryInfoSection}>
            <Text style={styles.sectionTitle}>Active Dietary Filters</Text>
            <Text style={styles.dietaryInfoText}>
              Your dietary preferences are automatically applied to all meals.
            </Text>
            {userPreferences.restrictions?.length > 0 && (
              <View style={styles.dietaryInfoItem}>
                <Ionicons name="leaf-outline" size={16} color={colors.primary} />
                <Text style={styles.dietaryInfoLabel}>
                  Restrictions: {userPreferences.restrictions.join(', ')}
                </Text>
              </View>
            )}
            {userPreferences.allergies?.length > 0 && (
              <View style={styles.dietaryInfoItem}>
                <Ionicons name="warning-outline" size={16} color={colors.warning} />
                <Text style={styles.dietaryInfoLabel}>
                  Allergies: {userPreferences.allergies.join(', ')}
                </Text>
              </View>
            )}
            <TouchableOpacity
              style={styles.editPreferencesButton}
              onPress={() => navigation.navigate('DietaryPreferences')}
            >
              <Ionicons name="settings-outline" size={16} color={colors.primary} />
              <Text style={styles.editPreferencesText}>Edit Dietary Preferences</Text>
            </TouchableOpacity>
          </View>
        )}


      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  clearButton: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '500',
  },
  sortSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  sortContainer: {
    gap: spacing.sm,
  },
  sortOption: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  sortOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  sortOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
  },
  sortOptionText: {
    flex: 1,
    marginLeft: spacing.md,
    marginRight: spacing.sm,
  },
  sortOptionLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  sortOptionLabelSelected: {
    color: colors.surface,
  },
  sortOptionDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  sortOptionDescriptionSelected: {
    color: colors.surface,
    opacity: 0.8,
  },
  dietaryInfoSection: {
    backgroundColor: colors.background,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  dietaryInfoText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: 18,
  },
  dietaryInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  dietaryInfoLabel: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  editPreferencesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: spacing.sm,
    alignSelf: 'flex-start',
  },
  editPreferencesText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },

});

export default MealFilterScreen;
