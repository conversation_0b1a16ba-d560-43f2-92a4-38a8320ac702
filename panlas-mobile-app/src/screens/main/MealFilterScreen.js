import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, fonts, spacing, borderRadius } from '../../styles/globalStyles';
import { commonStyles } from '../../styles/commonStyles';
import { mealsAPI } from '../../services/api';

const MealFilterScreen = ({ navigation, route }) => {
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [allMeals, setAllMeals] = useState([]);

  const dietaryFilters = [
    { id: 'Vegetarian', label: 'Vegetarian', icon: '🌱', color: '#4CAF50' },
    { id: 'Vegan', label: 'Vegan', icon: '🌿', color: '#2E7D32' },
    { id: 'Gluten-Free', label: 'Gluten-Free', icon: '🌾', color: '#FF9800' },
    { id: 'Dairy-Free', label: 'Dairy-Free', icon: '🥛', color: '#2196F3' },
    { id: 'Nut-Free', label: 'Nut-Free', icon: '🥜', color: '#9C27B0' },
    { id: 'Low-Carb', label: 'Low-Carb', icon: '⚡', color: '#F44336' },
    { id: 'Keto', label: 'Keto', icon: '🔥', color: '#E91E63' },
    { id: 'Pescatarian', label: 'Pescatarian', icon: '🐟', color: '#00BCD4' },
    { id: 'Halal', label: 'Halal', icon: '🕌', color: '#4CAF50' }
  ];

  useEffect(() => {
    loadAllMeals();
  }, []);

  useEffect(() => {
    filterMeals();
  }, [selectedFilters, allMeals]);

  const loadAllMeals = async () => {
    try {
      setLoading(true);
      const response = await mealsAPI.getFilipinoDishes();
      setAllMeals(response.data || []);
    } catch (error) {
      console.error('Error loading meals:', error);
      Alert.alert('Error', 'Failed to load meals');
    } finally {
      setLoading(false);
    }
  };

  const filterMeals = () => {
    if (selectedFilters.length === 0) {
      setFilteredMeals(allMeals);
      return;
    }

    const filtered = allMeals.filter(meal => {
      return selectedFilters.every(filter => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};
        
        switch(filter) {
          case 'Vegetarian':
            return dietType.isVegetarian;
          case 'Vegan':
            return dietType.isVegan;
          case 'Gluten-Free':
            return dietType.isGlutenFree;
          case 'Dairy-Free':
            return dietType.isDairyFree;
          case 'Nut-Free':
            return dietType.isNutFree;
          case 'Low-Carb':
            return dietType.isLowCarb;
          case 'Keto':
            return dietType.isKeto;
          case 'Pescatarian':
            return dietType.isPescatarian;
          case 'Halal':
            return dietType.isHalal;
          default:
            return false;
        }
      });
    });

    setFilteredMeals(filtered);
  };

  const toggleFilter = (filterId) => {
    setSelectedFilters(prev => {
      if (prev.includes(filterId)) {
        return prev.filter(id => id !== filterId);
      } else {
        return [...prev, filterId];
      }
    });
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
  };

  const renderFilterButton = ({ item }) => {
    const isSelected = selectedFilters.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.filterButton,
          isSelected && { backgroundColor: item.color, borderColor: item.color }
        ]}
        onPress={() => toggleFilter(item.id)}
      >
        <Text style={styles.filterIcon}>{item.icon}</Text>
        <Text style={[
          styles.filterText,
          isSelected && styles.filterTextSelected
        ]}>
          {item.label}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderMealCard = ({ item: meal }) => {
    const dietType = meal.dietType || meal.dietaryAttributes || {};
    
    return (
      <TouchableOpacity
        style={styles.mealCard}
        onPress={() => navigation.navigate('MealDetail', { meal })}
      >
        <Image
          source={{ uri: meal.image || 'https://via.placeholder.com/150x100' }}
          style={styles.mealImage}
          resizeMode="cover"
        />
        <View style={styles.mealInfo}>
          <Text style={styles.mealName} numberOfLines={2}>
            {meal.name}
          </Text>
          <Text style={styles.mealCalories}>
            {meal.calories || 0} calories
          </Text>
          
          {/* Dietary badges */}
          <View style={styles.dietaryBadges}>
            {dietType.isVegetarian && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#4CAF50' }]}>
                <Text style={styles.dietaryBadgeText}>🌱</Text>
              </View>
            )}
            {dietType.isVegan && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#2E7D32' }]}>
                <Text style={styles.dietaryBadgeText}>🌿</Text>
              </View>
            )}
            {dietType.isGlutenFree && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#FF9800' }]}>
                <Text style={styles.dietaryBadgeText}>🌾</Text>
              </View>
            )}
            {dietType.isDairyFree && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#2196F3' }]}>
                <Text style={styles.dietaryBadgeText}>🥛</Text>
              </View>
            )}
            {dietType.isLowCarb && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#F44336' }]}>
                <Text style={styles.dietaryBadgeText}>⚡</Text>
              </View>
            )}
            {dietType.isKeto && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#E91E63' }]}>
                <Text style={styles.dietaryBadgeText}>🔥</Text>
              </View>
            )}
            {dietType.isHalal && (
              <View style={[styles.dietaryBadge, { backgroundColor: '#4CAF50' }]}>
                <Text style={styles.dietaryBadgeText}>🕌</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Filter Meals</Text>
        <TouchableOpacity onPress={clearAllFilters}>
          <Text style={styles.clearButton}>Clear</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Filter Section */}
        <View style={styles.filterSection}>
          <Text style={styles.sectionTitle}>Dietary Restrictions</Text>
          <FlatList
            data={dietaryFilters}
            renderItem={renderFilterButton}
            keyExtractor={(item) => item.id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.filtersContainer}
          />
        </View>

        {/* Results Section */}
        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>
            Results ({filteredMeals.length} meals)
          </Text>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>Loading meals...</Text>
            </View>
          ) : (
            <FlatList
              data={filteredMeals}
              renderItem={renderMealCard}
              keyExtractor={(item, index) => (item.id || item._id || index).toString()}
              scrollEnabled={false}
              contentContainerStyle={styles.mealsContainer}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
                  <Text style={styles.emptyTitle}>No meals found</Text>
                  <Text style={styles.emptySubtitle}>
                    Try adjusting your dietary filters
                  </Text>
                </View>
              }
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  clearButton: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '500',
  },
  filterSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  filtersContainer: {
    gap: spacing.sm,
  },
  filterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.sm,
  },
  filterIcon: {
    fontSize: 16,
    marginRight: spacing.sm,
  },
  filterText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '500',
    flex: 1,
  },
  filterTextSelected: {
    color: colors.surface,
  },
  resultsSection: {
    padding: spacing.md,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  loadingText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  mealsContainer: {
    gap: spacing.md,
  },
  mealCard: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    ...commonStyles.shadowSmall,
  },
  mealImage: {
    width: 100,
    height: 75,
    borderRadius: borderRadius.small,
  },
  mealInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  mealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  mealCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  dietaryBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  dietaryBadge: {
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    minWidth: 24,
    alignItems: 'center',
  },
  dietaryBadgeText: {
    fontSize: 12,
    color: colors.surface,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
});

export default MealFilterScreen;
